/* EmbeddedCodeSection styles */
.container {
  /* Ensure content is centered */
  @apply text-center;
}

/* Make iframe responsive and centered */
.container iframe {
  /* Responsive width, max width not exceeding container */
  max-width: 100% !important;
  height: auto !important;
  /* Maintain aspect ratio */
  aspect-ratio: 16/9;
  /* Center display */
  margin: 0 auto !important;
  display: block !important;
}

/* Special handling for YouTube and other video iframes */
.container iframe[src*="youtube"],
.container iframe[src*="vimeo"],
.container iframe[src*="video"] {
  width: 100% !important;
  max-width: 800px !important;
  height: auto !important;
  aspect-ratio: 16/9 !important;
}

/* Special handling for Calendly widgets - only on screens 1025px and below */
@media (max-width: 1025px) {
  .container .calendly-inline-widget {
    width: 100% !important;
    max-width: 800px !important;
    height: 800px !important;
    margin: 0 auto !important;
    display: block !important;
    position: relative !important;
  }

  .container .calendly-inline-widget iframe {
    width: 100% !important;
    height: 100% !important;
    aspect-ratio: unset !important;
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
  }

  /* Override any Calendly mobile specific styles */
  .container .calendly-mobile {
    height: 800px !important;
  }
}
