/* EmbeddedCodeSection styles */
.container {
  /* Ensure content is centered */
  @apply text-center;
}

/* Make iframe responsive and centered */
.container iframe {
  /* Responsive width, max width not exceeding container */
  max-width: 100% !important;
  height: auto !important;
  /* Maintain aspect ratio */
  aspect-ratio: 16/9;
  /* Center display */
  margin: 0 auto !important;
  display: block !important;
}

/* Special handling for YouTube and other video iframes */
.container iframe[src*="youtube"],
.container iframe[src*="vimeo"],
.container iframe[src*="video"] {
  width: 100% !important;
  max-width: 800px !important;
  height: auto !important;
  aspect-ratio: 16/9 !important;
}

/* Special handling for Calendly widgets - only on screens 1025px and below */
@media (max-width: 1100px) {
  /* Use highly specific selectors to override <PERSON><PERSON>ly's inline styles */
  .container div[data-url*="calendly"].calendly-inline-widget,
  .container div[data-url*="calendly"].calendly-inline-widget.calendly-mobile,
  .container div.calendly-inline-widget[data-processed="true"] {
    width: 100% !important;
    max-width: 800px !important;
    height: 800px !important;
    min-height: 800px !important;
    margin: 0 auto !important;
    display: block !important;
    position: relative !important;
    /* Override any inline styles */
    -webkit-overflow-scrolling: touch !important;
  }

  .container div[data-url*="calendly"] iframe,
  .container div.calendly-inline-widget iframe,
  .container div.calendly-mobile iframe {
    width: 100% !important;
    height: 800px !important;
    min-height: 800px !important;
    max-height: 800px !important;
    aspect-ratio: unset !important;
    position: relative !important;
    top: 0 !important;
    left: 0 !important;
    border: none !important;
  }

  /* Force override using attribute selector */
  .container [data-url*="calendly"][data-processed="true"] {
    height: 800px !important;
    min-height: 800px !important;
    max-height: 800px !important;
  }
}
